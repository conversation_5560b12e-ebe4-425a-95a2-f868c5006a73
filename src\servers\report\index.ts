// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** API基础路径 */
const API_BASE = "/api";


//获取评估报告
export async function getTaskReportSummaryDetail(data: any) {
    try {
      const res = await request('/llmapi/report/detail', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        success: true,
      };
    } catch (error) {
      console.error('获取报告总结失败', error);
      return { data: [], total: 0, success: false };
    }
  }

// 获取报告总览

export async function getReportSummary(data: any ) {
    try {
      const res = await request('/llmapi/report/summary', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        success: true,
      };
    } catch (error) {
      console.error('获取报告总结失败', error);
      return { data: [], total: 0, success: false };
    }
  }





//获取评估报告
export async function getMissionList(data: any  ) {
    try {
      const res = await request<API.MissionItem>('/llmapi/mission/list', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        total: res.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务列表失败', error);
      return { data: [], total: 0, success: false };
    }
  }


  