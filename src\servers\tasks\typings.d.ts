// @ts-ignore
/* eslint-disable */

declare namespace API {

  // 任务列表
  type TaskList = {
    status?: number;
    data?: TaskItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  // 任务
  type TaskItem = {
    /**
     * TC260 or  TC260.生成内容测试题库
     */
    classification_id: string;
    /**
     * 2025-05-20T20:20:20
     */
    create_time: string;
    description: string;
    /**
     * "llm" or "keyword"
     */
    evaluate_method: EvaluationMethod;
    /**
     * 2025-05-21T20:20:20
     */
    finish_time: string;
    /**
     * ID 编号
     */
    id: string;
    /**
     * 目标 lllms 地址
     */
    model_id: string;
    /**
     * 名称
     */
    name: string;
    progress: number;
    /**
     * Task 状态
     */
    status: string;
  };

  /**
   * "llm" or "keyword"
   *
   * 评估方法
   */
  type EvaluationMethod = {
    /**
     * 评估参数
     */
    args: any[] | boolean | number | { [key: string]: any } | null | string;
    /**
     * 名称
     */
    name: string;
  };
  // MissionItem 类型
type MissionItem = {
  /**
   * ID 编号
   */
  id: string;
  /**
   * 使用的 usecase
   */
  usecase: any;
  /**
   * mission 归属的 task
   */
  task: any;
  mission_status: string;
  create_time: string;
  finish_time: string;
  /**
   * 原始数据
   */
  raw_data: any;
  /**
   * 评估结果
   */
  evaluate_result: any;
};

// MissionList 类型
type MissionList = {
  status?: number;
  data?: MissionItem[];
  /** 列表的内容总数 */
  total?: number;
  success?: boolean;
};

//敏感词任务类型
type CorpusTaskItem = {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'canceled';
  progress: number;
  created_at: string;
  updated_at: string;
  file_name: string;
  dict_id: string;
  results?: Results;
  error_message?: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
};
//敏感词任务列表类型
type CorpusTaskList = {
  status?: number;
  data?: CorpusTaskItem[];
  /** 列表的内容总数 */
  total?: number;
  success?: boolean;
};

}