export const SceneList = [
  
  {
    scene_id: 'AutoAttack',
    scene_name: '自动化攻防',
    // model_api: 'http://localhost:9999/nschat/8RsBdqOPaJ59SoG6NI9',
    // model_api: 'http://***********:8002/nschat/8RsBdqOPaJ59SoG6NI9',
    model_api: 'http://************:8895/nschat/8RsBdqOPaJ59SoG6NI9',
    hot_questions: [
      '查询下武汉市天气',
      '扫描************开放了哪些端口',
      '查一下*************的相关威胁情报',
      '扫描一下 http://************:8001/ 的目录',
    ],
    scene_desc:
      '欢迎您使用风云卫大模型，作为一个自动化攻防的大模型，我可以通过您的指令对靶场环境进行自动化渗透测试，靶标分析，EASM报告的工作。 您可以说：帮我看看A公司的外部资产暴露情况？',
    payload: (value: any) => {
      return {
        query: value
      };
    },
  }
];
