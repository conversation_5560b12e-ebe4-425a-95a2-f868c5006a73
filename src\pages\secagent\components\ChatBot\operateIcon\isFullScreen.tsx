import React, { useState, useEffect } from 'react';

function IsFullScreen() {
  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    // 监听全屏状态改变事件
    const handleFullScreenChange = () => {
      setIsFullScreen(document.fullscreenElement !== null);
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, []);

  const handleFullScreenClick = () => {
    if (!isFullScreen) {
      enterFullScreen();
    } else {
      exitFullScreen();
    }
  };

  const enterFullScreen = () => {
    const contentElement = document.querySelector('.content');
    if (contentElement && contentElement.requestFullscreen) {
      contentElement.requestFullscreen();
    }
  };

  const exitFullScreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  };

  const handleKeyPress = (event: { key: string; }) => {
    if (event.key === 'Escape' && isFullScreen) {
      exitFullScreen();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [isFullScreen]);
  return (
    <div className="full-screen-content">
      <div className={`content${isFullScreen ? ' fullscreen' : ''}`}>
        <span onClick={handleFullScreenClick}>{isFullScreen ? '取消全屏' : '全屏'}</span>
        {isFullScreen && <span>按 ESC 退出全屏</span>}
      </div>
    </div>
  );
}

export default IsFullScreen;
