.markdown-Content {
  h1,
  h2,
  h3,
  h4 {
    overflow: hidden;
    font-weight: 600;
    word-wrap: break-word;
    word-break: break-all;
  }
  p {
    // margin: 10px 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    line-height: 30px;
    word-wrap: break-word;
    // 自动换行
    word-break: break-all;
  }
  img {
    padding-left: 10px;
    display: block;
  }
  table {
    width: 100%;
    margin: 12px 8px;
    font-size: 14px;
    // background: #fff;
    text-align: left;
    border-collapse: collapse;
  }
  th {
    padding: 0 8px;
    line-height: 40px;

    /* 表头样式 */
    // background-color: #f5f8f2; /* 设置表头的背景色 */
    border: 1px solid #e1e1e1;
  }

  td {
    min-width: 30px;
    padding: 0 8px;
    overflow: hidden;
    // background-color: #ffffff;
    line-height: 40px;
    word-wrap: break-word;
    // 超长换行
    word-break: break-all;

    /* 表格数据单元格样式 */
    border: 1px solid #e1e1e1;
    a {
      float: none !important;
      color: #55a722 !important;
      text-align: left !important;
      text-decoration: underline;
    }
  }
  ol,
  ul {
    margin: 8px 0;
    padding: 5px 20px;
    color: #353535;
    font-size: 14px;
    // background: #efefef;
    background: #eee;
  }

  ol {
    list-style-type: decimal; /* 设置有序列表的默认数字标志 */
  }

  li {
    line-height: 24px;
  }
  // pre {
  //   margin-top: 4px;
  //   padding: 1rem;
  //   font-size: 14px;
  //   line-height: 22px;
  //   // font-style: italic;
  //   // 超宽折行
  //   // white-space: pre-wrap;
  //   border-radius: 0.3em;
  //   display: block;
  //   overflow-x: auto;
  //   background: #000;
  //   // background: rgb(51, 51, 51);
  //   color: white;
  // }

  
}

/* 添加样式以增强代码块的可读性和用户体验 */
.code-container {
  position: relative;
}

.copy-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #000;
  border: none;
  padding: 5px;
  cursor: pointer;
  font-size: 14px;
}


// 打字机样式
.typewriter-text {
  overflow: hidden; /* 隐藏溢出文本 */
  animation: typing 3s steps(40, end); /* 可根据需要调整持续时间和步数 */
}
