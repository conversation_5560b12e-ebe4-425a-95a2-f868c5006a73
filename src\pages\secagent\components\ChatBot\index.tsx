// src/components/ChatBot.js
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  List,
  Avatar,
  AutoComplete,
  Tooltip,
  message,
  Col,
  Row,
  Input,
  Select,
  Collapse
} from 'antd';
import { FireOutlined, LoadingOutlined, SendOutlined, ToolOutlined } from '@ant-design/icons';
import './index.less';
import {
  addDialogSer,
  sendQuestion,
  getDialogHistory,
  getDialogList,
} from '../../service';
import type { IsChatBotProps } from '../../data';
import MarkdownContent from './markdown/markdownContent';
import SSE from './sseSer/sse';
import ChatBotPng from '@/static/assets/logo.svg';
import UserPng from '@/static/assets/bird.svg';
import clearSvg from '@/static/assets/clear.svg';
// import UploadFile from './operateIcon/uploadFile';
import stop from '@/static/assets/stop.svg';
import { SceneList } from '../../constant';
import gptTitle from '@/static/assets/gpt-title.svg';
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;


interface ToolChain {
  chain_index: number,
  tool_kwargs: {
    tool?: string;
    tool_input?: string;
    log?: string;
  },
  tool_response: {
    data?: {}
  }
}
/**
 * 修改URL的前后增加空格
 */
function modifyUrls(text:string) { 
  let urlRegex = /(https?:\/\/[^\s]+)/g;

  let replacedText = text.replace(urlRegex, function(url) {
    // 在URL前后加上空格
    return ' ' + url + ' ';
  });
  console.log(replacedText);
  return replacedText;
}

const ChatBot = (props: IsChatBotProps) => {
  const {
    currentDialog,
    setCurrentDialog,
    setDialogs,
    setSceneData,
    sceneData,
    setMessages,
    messages,
    inputValue,
    setInputValue,
    typingText,
    setTypingText,
  } = props;
  // const [messages, setMessages] = useState([]);
  const [sceneName, setSceneName] = useState<string>('AutoAttack');
  // const [inputValue, setInputValue] = useState('');
  const chatMessageRef = useRef(null);
  const [optionHot, setOptionHot] = useState([]);
  const [loading, setLoading] = useState(false);
  const chatSSE = useRef<SSE | null>(null);
  // const [typingText, setTypingText] = useState(false); // 保存正在打字的内容
  const [receivedIsDone, setReceivedIsDone] = useState('done');
  const onChangeSelect = (e: any) => {
    setInputValue(e);
  };

  // 组装messages数据，多轮对话函数
  const gethistoryMessage = (data: any) => {
    const msgArr: { role: string; content: any }[] = [];
    for (let i = 0; i < Math.min(data.length, 5); i++) {
      if (data[i].answer) {
        msgArr.push({
          role: 'assistant',
          content: data[i].answer,
        });
      }
      if (data[i].question) {
        msgArr.push({
          role: 'user',
          content: data[i].question,
        });
      }
    }
    return msgArr;
  };

  // 判断是否文件上传
  const handleSendMessage = useCallback(async () => {
    setLoading(true); // 请求出错后设置 loading 为 false
    if (inputValue === '' || inputValue === '/') {
      message.warning('请输入内容');
      return;
    }
    const currentId = generateUUID();
    if (currentDialog === '') {
      addDialog(currentId);
      setCurrentDialog(currentId);
    }

    // 输入框对话内容
    const newMessage = {
      question: inputValue,
      answer: '',
      dialog_uuid: currentDialog,
      data: '',
      tools: [{}],
    };
    setMessages((prevMessages) => [...prevMessages, newMessage]);

    if (inputValue.trim() !== '') {
      setInputValue('');
    }

    // console.log(messages, 'allMessages');
    // 把newMessage添加到messages数组中;
    const allMessages = messages.concat([newMessage]).reverse().slice(0, 5);
    const payloadData = sceneData.payload(inputValue);
    if (payloadData.hasOwnProperty('messages')) {
      payloadData.messages = gethistoryMessage(allMessages).reverse();
    }
    const url = sceneData?.model_api; //uploadMessage?.knowledge_base_id ? urlFile : urlChat;
    console.log('url:',url);
    
    chatSSE.current = new SSE(url, {
      method: 'POST',
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json', // 根据你的后端接口要求设置请求头
        Authorization: localStorage.getItem('x-auth-token'),
        // Authorization: 'Bearer ' + localStorage.getItem('x-auth-token'),
      },
      payload: JSON.stringify(payloadData),
    });
    chatSSE.current.stream(); // 开始流式接收数据

    const arrList: any[] = [];
    setReceivedIsDone('start');
    chatSSE.current.addEventListener('readystatechange', (event: any) => {
      switch (event.readyState) {
        case 1:
          setTypingText(true);
          break;
        case 2:
          setLoading(false);
          setTypingText(false);
          // 非流式输出，获取最后的结果
          if (!payloadData.stream) {
            if (event && event.source && event.source.xhr?.response) {
              const resultData = JSON.parse(event.source.xhr.response);

              if (
                resultData &&
                resultData.choices &&
                resultData.choices[0]?.message &&
                resultData?.choices[0].message.content
              ) {
                // 直接输出对话内容
                // newMessage.answer = newMessage.answer.concat(resultData.choices[0].message.content);
                newMessage.answer = newMessage.answer.concat(modifyUrls(resultData.choices[0].message.content));
                setMessages([...messages, newMessage]); // 将机器人回复添加到聊天会话中

                // 获取思考过程-工具调用链
                if (resultData.choices[0].message.intermediate_steps) {
                  const intermediate_steps = resultData.choices[0].message.intermediate_steps;
                  newMessage.data = intermediate_steps

                  console.log('intermediate_steps:', newMessage.data);
                  // intermediate_steps.forEach((toolChain: ToolChain) => {
                  //   console.log('toolChain.tool_kwargs.tool', toolChain);
                  // })

                }
              }


              const qaData = {
                dialog_uuid: currentDialog !== '' ? currentDialog : currentId,
                question: newMessage.question,
                // answer: JSON.stringify(newMessage.answer),
                answer: newMessage.answer,
                // data: newMessage.data,
              };
              if (sceneData || sceneName) {
                const sceneSend = {
                  scene_id: sceneData.scene_id || sceneName,
                  scene_name:
                    sceneData.scene_name ||
                    SceneList.filter((item) => item.scene_id === sceneName)[0].scene_name,
                };
                Object.assign(qaData, sceneSend);
              }
              sendQa(qaData);
            }
          }
          setReceivedIsDone('done');
          setInputValue('');
          break;
        default:
          break;
      }
    });

    chatSSE.current.addEventListener('postmessage', (event: any) => {
      console.log('postmessage event.data:', event, event.data);
    });
    // 监听 message 事件，接收服务器推送的数据
    chatSSE.current.addEventListener('message', (event: any) => {
      if (event.data && event.data !== '' && event.data !== '[DONE]' && event.data !== 'ping') {
        setLoading(false);

        const messageData = JSON.parse(event.data);
        console.log('messageData:', messageData);

        localStorage.setItem('messageData', JSON.stringify(arrList));
        const messagetype = payloadData.stream ? 'delta' : 'message';
        if (
          messageData &&
          messageData.choices &&
          messageData.choices[0]?.[messagetype] &&
          messageData?.choices[0][messagetype].content
        ) {
          // 模拟机器人打字效果
          newMessage.answer = newMessage.answer.concat(messageData.choices[0][messagetype].content);
          setMessages([...messages, newMessage]); // 将机器人回复添加到聊天会话中
          //
        }
        if (messageData.static_data) {
          // 统计图
          newMessage.data = messageData.static_data;
          // console.log('static_data:', newMessage);
          setMessages([...messages, newMessage]);
        }
      } else {
        // 发送QA对话进行存储
        if (event.data === '[DONE]') {
          setLoading(false);
          const qaData = {
            dialog_uuid: currentDialog !== '' ? currentDialog : currentId,
            question: newMessage.question,
            // answer: JSON.stringify(newMessage.answer),
            answer: newMessage.answer,
            data: newMessage.data,
          };
          if (sceneData || sceneName) {
            const sceneSend = {
              scene_id: sceneData.scene_id || sceneName,
              scene_name:
                sceneData.scene_name ||
                SceneList.filter((item) => item.scene_id === sceneName)[0].scene_name,
            };
            Object.assign(qaData, sceneSend);
          }
          sendQa(qaData);
        }
      }
    });
  }, [inputValue]);

  const addDialog = (currentId: string) => {
    setCurrentDialog(currentId);
    const addData = {
      dialog_uuid: currentId,
      dialog_name: inputValue,
      scene_id: sceneName,
      scene_name: SceneList.filter((item) => item.scene_id === sceneName)[0].scene_name,
      dialog_type: 'SecAgent'
    };
    // 调用addDialogSer;，传入addData
    addDialogSer(addData).then(async ({ data }) => {
      if (data) {
        // message.success('新建会话成功');
        setTimeout(() => {
          getSideBar();
          setInputValue('');
        }, 1000);
      }
    });
  };
  const getSideBar = () => {
    // 获取会话信息 getDialogList
    getDialogList().then(({ data }) => {
      if (data && data.length > 0) {
        setDialogs(data);
        setCurrentDialog(data[0].dialog_uuid);
      }
    });
  };

  // 发送存储QA对话
  const sendQa = useCallback((qaData) => {
    // 调用接口 sendQuestion
    sendQuestion(qaData).then(({ data }) => {
      console.log('存储成功');
    });
  }, []);

  // 手动终止流式接收数据
  const stopStream = useCallback(() => {
    // 如果存在 SSE 对象，则关闭它
    if (chatSSE.current) {
      chatSSE.current.close(); // 关闭 SSE 对象
      chatSSE.current = null;
    }
  }, []);
  const getMessages = (historyData: any) => {
    // 获取历史消息
    getDialogHistory({ dialog_uuid: historyData }).then(({ data }) => {
      setMessages(data);
    });
  };
  useEffect(() => {
    if (sceneData && sceneData.scene_id) {
      setSceneName(sceneData.scene_id);
      const scene = SceneList.filter((item) => item.scene_id === sceneData.scene_id)[0];
      setOptionHot(scene.hot_questions);
    }
  }, [sceneData]);

  useEffect(() => {
    if (receivedIsDone === 'done' && currentDialog !== '') {
      // getMessages(currentDialog);
      // getSideBar();
    }
  }, [receivedIsDone]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    handleSceneChange(sceneName);
  }, []);
  const scrollToBottom = () => {
    if (chatMessageRef.current) {
      chatMessageRef.current.scrollTop = chatMessageRef.current.scrollHeight;
    }
  };
  const clearMessages = () => {
    setMessages([]);
  };

  // 生成唯一的UUId
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const renderAvatar = (sender: string) => {
    // const icon = sender === 'user' ? <UserOutlined /> : <RobotOutlined />;
    const className = sender === 'user' ? 'avatar-user' : 'avatar-chatbot';
    const avatarImage = sender === 'user' ? UserPng : ChatBotPng;
    return <Avatar src={avatarImage} className={className} />;
  };
  const handleSelect = (value: any) => {
    setInputValue(value);
  };
  /**
   * 场景选项发生变化，对应的model,url，热点问题变化也发生变化
   * @param value
   */
  const handleSceneChange = (value: any) => {
    setInputValue('');
    setSceneName(value);
    const scene = SceneList.filter((item) => item.scene_id === value)[0];
    setSceneData(scene);
    setOptionHot(scene.hot_questions);
  };

  const handleKeyPress = (e: { key: string; shiftKey: any; preventDefault: () => void }) => {
    // 按下回车键
    if ((inputValue === '' || inputValue === '/') && e.key === 'Enter') {
      e.preventDefault(); // 阻止默认的Enter行为
      return;
    }
    if (e.key === 'Enter') {
      if (!e.shiftKey) {
        handleSendMessage();
        e.preventDefault(); // 阻止默认的Enter行为
        // 清空inputValue
      } else {
        setInputValue(inputValue);
        return;
      }
    }
  };
  return (
    <div className="chatbot-containers">
      {/* <div className="chat-title">⭐SecLLm </div> */}
      {messages.length === 0 && !loading && (
        <div className="hot-container">
          <div className="title-name">
            <img src={gptTitle} alt="" />
            {/* {renderAvatar('chat-bot')}SecLLM */}
          </div>
          <div className="hot-title">
            <FireOutlined />
            热门推荐
          </div>
          <div className="hot-content">
            <Row gutter={8}>
              {optionHot?.map((item: any, index: number) => (
                <Col span={12} key={index}>
                  <div className="hot-item" onClick={() => handleSelect(item)}>
                    {item}
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </div>
      )}
      <div className="chat-box" ref={chatMessageRef}>
        <div className="chat-message">
          {messages.length > 0 && (
            <List
              dataSource={messages}
              renderItem={(item: any, index: number) => (
                <List.Item className="dialog-item" key={index}>
                  <div className="message-item">
                    <div
                      className="message-content message-item-right"
                      style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}
                    >
                      {/* <MarkdownContent content={item.question} /> */}
                      {item.question}
                    </div>
                    <div className="avatar-container">{renderAvatar('user')}</div>
                  </div>
                  {item.answer !== '' && (
                    <div className="message-item">
                      <div className="avatar-container">{renderAvatar('chat-bot')}</div>
                      <div className="message-content message-item-left">
                        <MarkdownContent content={item.answer} />
                        {item.data && (
                          <div
                            style={{
                              width: '100%',
                              minWidth: '520px',
                              margin: '20px 0',
                              // height: '200px',
                            }}
                          >
                            <Collapse expandIconPosition={'end'}>
                              {item.data?.map((toolChain: ToolChain) => {
                                const ptext = '#### 思维链   \n' + toolChain.tool_kwargs.log + '\n#### 输出    \n ```json \n' + JSON.stringify(toolChain.tool_response?.data, null, 2) + '\n``` ';
                                return (
                                  <>
                                    <Panel header={[<ToolOutlined />, ` 调用工具：` + toolChain.tool_kwargs.tool]} key={toolChain.chain_index} extra={'🤖'} >
                                      <MarkdownContent content={ptext} />
                                    </Panel>
                                  </>)
                              })}
                            </Collapse>

                          </div>
                        )}
                        {/* <FeedBack
                          setMessages={setMessages}
                          dataItem={item}
                          receivedIsDone={receivedIsDone}
                        /> */}
                      </div>
                    </div>
                  )}
                </List.Item>
              )}
            />
          )}
          {loading && (
            <List.Item className="dialog-item">
              <div className="message-item">
                <>
                  <div className="avatar-container">{renderAvatar('chatbot')}</div>
                  <div className="message-content message-item-left">
                  分析中…
                    <LoadingOutlined />
                  </div>
                </>
              </div>
            </List.Item>
          )}
        </div>
      </div>
      <div className="input-container">
        <div className="upload-container">
          <div className="operate-icon">
            <Select
              className={`select-scene ${currentDialog !== '' || loading || typingText ? 'loading-disable' : ''
                }`}
              value={sceneName}
              bordered={false}
              onChange={handleSceneChange}
              placeholder="请选择场景"
            >
              {SceneList?.map((item: any, index: number) => (
                <Option key={item.scene_id} value={item.scene_id}>
                  {item.scene_name}
                </Option>
              ))}
            </Select>
            {/* <div className={`${loading || typingText ? 'loading-disable' : ''}`}>
              <UploadFile
                inputValue={inputValue}
                sceneName={sceneName}
                setMessages={setMessages}
                setDialogs={setDialogs}
                currentDialog={currentDialog}
                setCurrentDialog={setCurrentDialog}
              />
            </div> */}
            {/* <VoiceToText setInputValue={setInputValue} /> */}
            {(loading || typingText) && (
              <div className="stop-dialog" onClick={stopStream}>
                <img src={stop} />
                <span>停止对话</span>
              </div>
            )}
            <div
              className={` ${loading || typingText ? 'loading-disable' : ''}`}
              style={{ position: 'absolute', right: '0px', bottom: '0px' }}
            >
              {optionHot?.map((item: any, index: number) => (
                <>
                  {index <= 2 && (
                    <Tooltip title={item}>
                      <span className="hot-tag" onClick={() => setInputValue(item)}>
                        {item}
                      </span>
                    </Tooltip>
                  )}
                </>
              ))}
            </div>
          </div>
        </div>
        <div className="input-group">
          <Tooltip title="清除会话内容">
            <span
              className={`input-icon ${loading || typingText || messages.length === 0 ? 'disabled-btn' : ''
                }`}
              onClick={clearMessages}
            >
              <img src={clearSvg} alt="" />
            </span>
          </Tooltip>
          <AutoComplete
            style={{ position: 'relative' }}
            value={inputValue}
            onSelect={(v: string) => onChangeSelect(v)}
            disabled={loading || typingText}
          >
            {inputValue === '/' &&
              optionHot?.map((item: any, index: number) => (
                <Option style={{ fontSize: '12px!important' }} key={item} value={item}>
                  {item.scene_name}
                </Option>
              ))}
            <TextArea
              placeholder="请输入 ' / ' 获取问题指令，或直接输入你的问题..."
              value={inputValue}
              className="scene-input"
              onChange={(e) => setInputValue(e.target.value)}
              disabled={loading || typingText}
              style={{
                minHeight: 54,
                maxHeight: 150,
                fontSize: '16px',
                borderRadius: '8px',
                paddingRight: '36px',
              }}
              onKeyDown={handleKeyPress}
            />
          </AutoComplete>
          <Button
            size="large"
            type="link"
            onClick={handleSendMessage}
            style={{ position: 'absolute', right: '-2px', bottom: '18px', fontSize: '24px' }}
            disabled={loading || typingText || inputValue === '' || inputValue === '/'}
          >
            <Tooltip title="发送消息">
              <SendOutlined style={{ fontSize: '24px' }} rotate={-30} />
            </Tooltip>
          </Button>
        </div>
        {loading && <div className="loader" />}
      </div>
    </div>
  );
};

export default React.memo(ChatBot);
