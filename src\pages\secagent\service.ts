import { request } from '@/servers/request';
const baseUrl = '/nlpapi/v1/ml/';
const SecUrl = '/api/v1/secllm/';
// Chat聊天接口数据 Post
export function getChatData(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(baseUrl + 'chat/chat_nskg', {
      data,
    });
  }
  
  // 获取历史记录接口 Get
  export function getHistoryData(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(baseUrl + 'chat/history_query', {
      data,
    });
  }
  
  // 获取热门推荐接口 Get
  export function getHotData(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(baseUrl + 'chat/hot_query', {
      data,
    });
  }


  // 获取会话接口
  export function getDialogList(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'dialog/filter', {});
  }

  // 新建会话接口
  export function addDialogSer(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'dialog/add', {
      data,
    });
  }

// 编辑会话名称
  export function editDialogSer(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'dialog/name', {
      data,
    });
  }

// 发送QA接口
  export function sendQuestion(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'question/add', {
      data,
    });
  }
  
// 正负反馈
  export function feedback(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'question/feedback', {
      data,
    });
  }

// 获取会话的历史聊天记录
  export function getDialogHistory(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'dialog/history', {
      data,
    });
  }

// 删除会话
  export function deleteDialogSer(data: any): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    return request.post(SecUrl + 'dialog/delete', {
      data,
    });
  }

// 上传文件
export function uploadFile(data: any): Promise<{
  code: number;
  message: string;
  data: any;
}> {
  return request.post(SecUrl + 'knowledge/add', {
    data,
  });
}
