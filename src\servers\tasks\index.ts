import { request } from '@/servers/request';

const API_BASE = "/api"


interface QueryParams {
  limit: number,
  offset: number,
  keyword: string,
}

export function getTasksList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  return request.post<API.TaskList>(API_BASE + '/task/list', {
    ...params,
    ...(sort || {}),
    ...(filter || {}),
  })
}

/** 获取任务列表 */
export function getMissonList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  const queryData = {
    ...params,
    ...(sort || {}),
    ...(filter || {}),
  }
  return request.post(API_BASE + '/mission/list', queryData)
}

/** 获取任务  POST api/task/{id} */
export function getTask(
  taskId: string
) {
  return request.get<API.TaskItem>(API_BASE + '/task/' + taskId);
}

/** 新建任务  POST api/task/create */
export function addTask(
  data: API.TaskItem
) {
  return request.post<API.TaskList>(API_BASE + '/task/create', data);
}

/** 更新任务  POST api/model/create */
export function updateTask(
  id: string,
  data: API.TaskItem
) {
  return request.put<API.TaskList>(API_BASE + `/task/${id}`,
    data,
  );
}


/** 删除任务 DELETE /api/task */
export function removeTask(id: string) {
  return request.delete<Record<string, any>>(API_BASE + `/task/${id}`);
}

/** 获取任务状态统计信息 */
export function getTastStatistics(
  id: string,
) {
  return request.get<Record<string, any>>(API_BASE + `/task/${id}/statistics`);
}
/** 重启task未完成mission */
export function retryTask(
  id: string,
) {
  return request.post<Record<string, any>>(API_BASE + `/task/${id}/retry`);
}
/** 列出敏感词任务列表 */
export function getCorpusTaskList(params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  const queryData = {
    ...params,
    ...(sort || {}),
    ...(filter || {}),
  }
  return request.post<API.CorpusTaskList>(API_BASE + '/sensitive/list',
    queryData,
  )
}

/** 创建敏感词任务 */
export function createCorpusTask(formData: FormData, onUploadProgress?: (progressEvent: any) => void) {
  return request.post<API.CorpusTaskList>(API_BASE + '/sensitive/create', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    onUploadProgress,
  });
}

/** 删除敏感词任务 DELETE /sensitive/{id} */
export function removeCorpusTask(id: string) {
  return request.delete<Record<string, any>>(API_BASE + `/sensitive/${id}`);
}

// /** 下载敏感词检测报告 GET /sensitive/report/{id} */
// export function downloadCorpusReport(id: string) {
//   return request(API_BASE + `/sensitive/report/${id}`, {
//     method: 'GET',
//     responseType: 'blob', // 设置响应类型为blob，用于文件下载
//     getResponse: true, // 获取完整的响应对象，包含headers
//   });
// }

/** 下载敏感词检测报告 GET /sensitive/report/{id} */
export function downloadCorpusReport(id: string) {
  return request.get(API_BASE + `/sensitive/report/${id}`, {
    responseType: 'blob',
    // 这一步很关键：让拦截器识别这是个下载请求，不执行 JSON 校验
    skipInterceptors: true,
  });
}

/** 修改mission */
export function updateMission(
  data: {
    mission_id: string,
    pass: boolean,
    reason: string,
  }
) {
  return request.post(API_BASE + `/mission/audit`,
    data);
}