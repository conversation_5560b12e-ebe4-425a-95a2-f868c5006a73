import { request } from '@/servers/request';

const API_BASE = "/api"


interface QueryParams {
  limit: number,
  offset: number,
  keyword: string,
}


/** 获取test模型列表  POST /api/llmapi/model/list */
export function getTestllmsList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  return request.post<API.LLMList>(API_BASE + '/model/list',
    {
      ...params,
      ...(sort || {}),
      ...(filter || {}),
    }
  );
}

/** 更新模型 */
export function updateTestLLM(id: string, data: any) {
  return request.put<API.LLMList>(API_BASE + `/model/${id}`, data
  );
}

/** 新建模型  POST api/model/create */
export function addTestLLM(
  data: API.LLMItem
) {
  return request.post<API.LLMList>(API_BASE + '/model/create',
    data
  );
}


/** 删除模型 DELETE /api/rule */
export function removeTestLLM(path: { id: string }) {
  const url = API_BASE + `/model/${path.id}`;
  return request.delete<API.LLMList>(url);
}

//获取评估报告
export function getTestResult(options: { id: string }) {
  const url = API_BASE + `/model/${options.id}`;
  return request.post<Record<string, any>>(url);
}

//获取模型详情
export function getTestllms(
  path: { id: string },
) {
  console.log("model_id", path.id);

  const url = API_BASE + `/model/${path.id}`;
  return request.get<API.LLMList>(url);
}

//测试链接连通性
export function testLink(
  data: API.LLMItem
) {
  return request.post<API.LLMList>(API_BASE + '/model/test', data
  );

}



export function getEvalllmsList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  const queryData = {
      ...params,
      ...(sort || {}),
      ...(filter || {}),
    }
  return request.post<API.LLMList>(API_BASE + '/model/list',
    queryData
  );
}

export function updateEvalLLM(id: string, data: any) {
  return request.put<API.LLMList>(API_BASE + `/model/${id}`, data
  );
}

/** 新建模型 POST /api/model/create */
export function addEvalLLM(data: {
  api_key?: string | null;
  api_url: string;
  description: string;
  internal_option: {
    max_tokens?: number | null;
    model: string;
    repetition_penalty?: number | null;
    temperature?: number | null;
    top_k?: number | null;
    top_p?: number | null;
  };
  llm_type: string;
  model_type: 'evaluate';
  name: string;
  template_option: {
    headers: Array<{ key: string; value: string }>;
    request_body: string;
    response_extract_rule: string;
  };
}) {
  return request.post<API.LLMList>(API_BASE + '/model/create', data
  );
}
/** 删除模型 DELETE /api/model/delete/{id} */
export function removeEvalLLM(path: { id: string }) {
  const url = API_BASE + `/model/${path.id}`;
  return request.delete<API.LLMList>(url);
}

/** 获取模型详情 GET /api/model/{id} */
export function getEvalLLM(
  path: { id: string },
) {
  const url = API_BASE + `/model/${path.id}`;
  return request.get<API.LLMList>(url);
}
