import React, { useState } from 'react';
import voice from '@/static/assets/voice.svg';
import { message } from 'antd';

function VoiceToText(props: { setInputValue: React.Dispatch<React.SetStateAction<string>> }) {
  const { setInputValue } = props;
  const [isListening, setIsListening] = useState(false);
  const [error, setError] = useState(null);

  const handleVoiceButtonClick = async () => {
    try {
      if (!('webkitSpeechRecognition' in window)) {
        message.error('当前浏览器不支持语音识别。');
        return;
      }
      const recognition = new window.webkitSpeechRecognition();
      recognition.lang = 'zh-CN'; // 设置中文语言代码
      recognition.interimResults = true; // 启用实时的中间结果
      setIsListening(true);
      setError(null);

      recognition.onresult = (event: { resultIndex: any; results: string | any[] }) => {
        let interimTranscript = '';
        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            const transcript = event.results[i][0].transcript;
            setInputValue(transcript);
          } else {
            interimTranscript += event.results[i][0].transcript;
            setInputValue(interimTranscript);
          }
        }
        // 在实时中间结果中更新
        setInputValue((prevText) => prevText + interimTranscript);
      };

      recognition.onerror = (event: any) => {
        message.warning('语音识别终止！');
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.start();
    } catch (error) {
      message.warning('语音识别终止！');
    }
  };

  return (
    <div onClick={handleVoiceButtonClick}>
      <div className="upload-icon">
        <img src={voice} alt="Voice Icon" />
        <span>语音 {isListening && <span className="tips">正在识别中...</span>}</span>
      </div>
    </div>
  );
}

export default VoiceToText;
