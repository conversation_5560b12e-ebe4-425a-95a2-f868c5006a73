// ChatBot的props
export interface IsChatBotProps {
  /** 当前页面的图名称 */
  graphName: string;
  /** 会话ID */
  currentDialog: string;
  sceneData: object;
  setSceneData: React.Dispatch<React.SetStateAction<>>;
  /** 操作会话ID的方法 */
  setCurrentDialog: React.Dispatch<React.SetStateAction<>>;
  //   /** 操作节点列表的方法 */
  setDialogs: React.Dispatch<React.SetStateAction<>>;
  dialogs: Array;
  setMessages: React.Dispatch<React.SetStateAction<>>;
  messages: Array;
  typingText: boolean;
  setTypingText: React.Dispatch<React.SetStateAction<>>;
}

export interface IsMaskProps {
  /** 当前页面的图名称 */
  graphName: string;
  // setInputValue: React.Dispatch<React.SetStateAction<NodeItem[]>>;
}

export interface EditItem {
  content: string;
  Checkbox: [];
}

export interface UploadData {
  // setUploadData: React.Dispatch<React.SetStateAction<>>;
  setCurrentDialog: React.Dispatch<React.SetStateAction<>>;
  //   /** 操作节点列表的方法 */
  setDialogs: React.Dispatch<React.SetStateAction<>>;
  // 当前会话Id
  currentDialog: string;
  // 刷新列表
  setMessages: React.Dispatch<React.SetStateAction<>>;
  inputValue: string;
  sceneName: any;
}

export interface FeedBackProps {
  // setUploadData: React.Dispatch<React.SetStateAction<>>;
  dataItem: {
    answer: string;
    dialog_uuid: string;
    feedback_content: string;
    feedback_labels: Array;
    feedback_status: number;
    qa_uuid: string;
    question: string;
  };
  //   /** 操作节点列表的方法 */
  setMessages: React.Dispatch<React.SetStateAction<>>;

  receivedIsDone: string;
}
