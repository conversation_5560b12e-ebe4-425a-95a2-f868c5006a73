import { DislikeOutlined, LikeOutlined } from '@ant-design/icons';
import { message } from 'antd';
import '../index.less';
import { useState } from 'react';
import CopyText from './copy';
import { ModalForm, ProFormCheckbox, ProFormTextArea } from '@ant-design/pro-form';
import { EditItem, FeedBackProps } from '../../../data';
import { feedback, getDialogHistory } from '../../../service';

const FeedBack = (props: FeedBackProps) => {
  const { dataItem, setMessages, receivedIsDone } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState('');
  // 正在编辑的用户信息对象
  const [editing, setEditing] = useState<EditItem>({
    content: '',
    Checkbox: [],
  });
  const openModal = async (type: string) => {
    setFeedbackType(type);
    setIsModalOpen(true);
    if (dataItem.feedback_status !== 0) {
      setEditing({
        content: dataItem.feedback_content,
        Checkbox:dataItem.feedback_labels,
      });
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };
  const renderModalTitle = () => {
    const classNode =
      feedbackType === 'like' ? 'like-icon' : feedbackType === 'dislike' ? 'dislike-icon' : '';
    return (
      <div className="feed-icon">
        <div className={`feedback-modal-title ${classNode}`}>
          {feedbackType === 'like' ? <LikeOutlined /> : <DislikeOutlined />}
        </div>
        <span className="text-color">
          {dataItem.feedback_status === 0 ? '提供反馈' : '查看反馈'}
        </span>
      </div>
    );
  };

  const onEditFinish = async (values: Partial<EditItem>) => {
    try {
      // 调用编辑接口,
      const feedbackData = {
        qa_uuid: dataItem.qa_uuid,
        feedback_status: feedbackType === 'like' ? 1 : 2,
        feedback_content: values.content,
        feedback_labels: values.Checkbox,
      };
      if (feedbackType === 'like') {
        delete feedbackData.feedback_labels;
      }
      feedback(feedbackData).then(async ({ data }) => {
        message.success('反馈成功!');
        // 关闭弹窗
        closeModal();
        // 刷新页面
        setTimeout(() => {
          getMessages();
        }, 1000);
      });
    } catch {
      message.error('反馈失败!');
      return false;
    }
  };
  const getMessages = () => {
    // 获取聊天记录消息
    getDialogHistory({ dialog_uuid: dataItem.dialog_uuid }).then(({ data }) => {
      setMessages(data);
    });
  };

  return (
    <div className="feed-back">
      {receivedIsDone === 'done' && (
        <a>
          <CopyText copyData={dataItem.answer} />
        </a>
      )}
      {dataItem.feedback_status !== 1 && receivedIsDone === 'done' && (
        <a onClick={() => openModal('dislike')}>
          <DislikeOutlined style={{ color: dataItem.feedback_status == 2 ? 'red' : 'gray' }} />
        </a>
      )}
      {dataItem.feedback_status !== 2 && receivedIsDone === 'done' && (
        <a onClick={() => openModal('like')}>
          <LikeOutlined style={{ color: dataItem.feedback_status == 1 ? 'green' : 'gray' }} />
        </a>
      )}

      <ModalForm
        title={renderModalTitle()}
        visible={isModalOpen}
        onFinish={onEditFinish}
        onVisibleChange={setIsModalOpen}
        modalProps={{ destroyOnClose: true }}
        initialValues={editing}
        width={500}
        className="feedback-modal"
        disabled={dataItem.feedback_status === 1 || dataItem.feedback_status === 2}
        // 校验是否为空
        validateMessages={{
          required: '此项为必填项',
        }}
      >
        {/* id */}
        <ProFormTextArea name="content" />

        {/* 用户id '有害/不安全', '不真实', '没有帮助'*/}

        {feedbackType === 'dislike' && (
          <ProFormCheckbox.Group
            name="Checkbox"
            options={[
              { label: '有害/不安全', value: 1 },
              { label: '不真实', value: 2 },
              { label: '没有帮助', value: 3 },
            ]}
          />
        )}
      </ModalForm>
    </div>
  );
};

export default FeedBack;