import { request } from '@/servers/request';

const API_BASE = "/api"


interface QueryParams {
  limit: number,
  offset: number,
  keyword: string,
}

/**
 * 获取数据总览数据
 * @param data - 请求数据
 */
export function getDataTrends(data: object) {
  return request.get('/dashboard', { params: data });
}
//获取字典列表
export function getDictList(
  data: {
    "limit": number,
    "offset": number,
    "module"?: string,
    "keyword": string,
    "parent_id"?: string,
  },
  options?: { [key: string]: any },
) {
  return request.post(API_BASE + '/dict/list',
    data
  );

}

//获取字典详情
export function getDictDetail(
  id: string,
) {
  return request.get(API_BASE + `/dict/${id}`);
}

//删除字典
export function removeDict(
  id: string,
  force?: boolean,
) {
  const url = API_BASE + `/dict/${id}`;
  const requestConfig: any = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 如果有 force 参数，作为查询参数传递
  if (force) {
    requestConfig.params = { force };
  }
  return request.delete(url, requestConfig);
  // return request.delete(url, requestConfig);
}


//获取字典树
export function getDictTree(
  parent_id: string,
) {
  const url = API_BASE + `/dict/nodes`;
  return request.post(url, { parent_id });
}
//获取用例列表
export function getUseCaseList(
  data: {
    limit: number,
    offset: number,
    classification_id?: string,
    keyword?: string,
    strategy_id?: string,
  },
) {
  return request.post(API_BASE + '/usecase/list', data
  );
}

//获取用例详情
export function getUseCaseDetail(
  params: { id: string },
) {
  const url = API_BASE + `/usecase/${params.id}`;
  return request.get(url);
}

//创建用例
export function createUseCase(
  body: {
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  return request.post(API_BASE + '/usecase/create',
    data);
}

//更新用例
export function updateUseCase(
  data: {
    id: string,
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  const url = API_BASE + `/usecase/${body.id}`;
  return request.put(url, data);
}

//删除用例
export function removeUseCase(options: { id: string }) {
  const url = API_BASE + `/usecase/${options.id}`;
  return request.delete(url);
}

//获取统计数据
export function getStatistics(
  id: string,
) {
  const url = API_BASE + `/dict/statistics/${id}`;
  return request.get(url);
}

//更新字典详情
export function updateDictDetail(id: string, data: any) {
  return request.put(API_BASE + `/dict/${id}`, data);
}

//获取策略列表
export function getPolicyList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {

  return request.post(API_BASE + '/strategy/list', params)

}


//获取策略详情
export function getPolicy(
  id: string
) {

  return request.get<API.StrategyItem>(API_BASE + '/strategy/' + id)
}

//添加策略
export function addStrategy(data: any) {

  return request.post(API_BASE + '/strategy/create', data
  );

}

//添加添加strategy到usecase
export function addStrategyToUsecase(
  data: {
    usecase_id_list?: any[];
    classification_id_list?: any[];
    strategy_id: string;
  },
) {
  return request.post<Record<string, any>>(API_BASE + '/strategy/usecase', data);

}

//删除策略
export function deleteStrategy(
  data: {
    id: string,
    force?: boolean,
  },
) {
  const url = API_BASE + `/strategy/delete`;

  return request.delete<Record<string, any>>(url, data);
}


//获取词库列表
export function getLexiconList(
  data: QueryParams,
) {
  return request.post<API.LexiconList>(API_BASE + '/sensitive/lexicon/list',
    data);

}

//删除词库
export function deleteLexicon(id: number | string) {
  return request.delete(API_BASE + `/sensitive/lexicon/${id}`);
}

//创建词库
export function createLexicon(data: {
  name: string;
  description?: string;
  tag?: string[];
}) {
  return request.post(API_BASE + '/sensitive/lexicon/create',
    data);
}

//更新词库
export function updateLexicon(id: number | string, data: {
  name?: string;
  description?: string;
  tag?: string[];
}) {
  return request.put(API_BASE + `/sensitive/lexicon/${id}`,
    data,
  );
}

//添加词条到词库
export function addWordToLexicon(data: {
  word_id_list?: string[];
  lexicon_id: string;
  rule_class?: string;
  tag?: string;
}) {
  return request.post(API_BASE + '/sensitive/lexicon/addword',
    data,
  );
}

//获取词条列表
export function getWordList(data: {
  limit: number;
  offset: number;
  keyword?: string;
  lexicon_id?: string;
  rule_class?: string;
  tag?: string;
}) {
  return request.post<API.WordList>(API_BASE + '/sensitive/lexicon/words',
    data,
  );
}

//获取词条分类列表
export function getWordCategoryList() {
  return request.get<API.WordCategoryList>(API_BASE + '/sensitive/lexicon/tree');
}