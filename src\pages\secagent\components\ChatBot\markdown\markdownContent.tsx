import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import RemarkGfm from 'remark-gfm';
import './markdown.less';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dark, darcula } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Button } from 'antd';
// 自定义样式对象
/**
 * 代码块
 * @param 
 * @returns 
 */
const codeBlockStyle = {
  position: 'relative',
  marginBottom: '1rem',
  minWidth: '100%',
  // maxHeight: '300px', // 设置最大高度
  overflow: 'auto', // 当内容超出时显示滚动条
  borderRadius: '8px',
};
const CodeBlock = ({ value, language, title}) => {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = () => {
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 3000); // Reset the copied status after 2 seconds
  };

  return (
    <div style={codeBlockStyle}>
      {/* {title && <div className="code-block-header">{title}</div>} */}
      <SyntaxHighlighter
        style={darcula}
        language={language}
        PreTag="div"
      // {...props}
      >
        {String(value).replace(/\n$/, '')}
      </SyntaxHighlighter>
      <CopyToClipboard text={value} onCopy={handleCopy}>
        <Button type="link" style={{
          position: 'absolute',
          top: '15px',
          right: '2px',
          color: 'white',
          fontSize: 12,
        }}>
          {isCopied ? '已复制!' : '复制'}
        </Button>
      </CopyToClipboard>
    </div>
  );
};

const MarkdownContent = (props: { content: string }) => {
  // 自定义`code`元素的渲染
  const components = {
    image: ({ alt, src, title }) => {
      // 在这里添加图片头信息，这里使用title属性
      const newsrc = '/chatimgs/' + src
      return <img alt={alt} src={newsrc} />;
    },
    code:(props) =>{
      const {children, className, node, ...rest} = props
      const match = /language-(\w+)/.exec(className || '')
      return match ? (
        // <div>
        // <SyntaxHighlighter
        //   {...rest}
        //   PreTag="div"
        //   children={String(children).replace(/\n$/, '')}
        //   language={match[1]}
        //   style={darcula}
        // />
        // </div>
        <CodeBlock value={String(children).replace(/\n$/, '')} language={match[1]}></CodeBlock>
      ) : (
        <code style={{
                  color: '#111827',
                  paddingLeft: '2px',
                  paddingRight: '2px',
                  // fontSize: '.900em',
                  fontWeight: 800,
                }}
          {...rest} className={className}>
          {children}
        </code>
      )
    }
  };
  const disableLinksPlugin = () => {
    return (tree) => {
      tree.children.forEach((node) => {
        if (node.type === 'link') {
          node.type = 'text';
        }
      });
    };
  };
  return (
    <ReactMarkdown
      className="markdown-Content"
      remarkPlugins={[RemarkGfm]} // 配置remark插件
      // skipHtml={true}
      components={components}
    >
      {props.content}
    </ReactMarkdown>
  );
};

export default MarkdownContent;
