import { useState } from 'react';
import { InboxOutlined } from '@ant-design/icons';
import { Modal, Upload, message } from 'antd';
import type { UploadData } from '../../../data';
import type { RcFile } from 'antd/es/upload/interface';
import upload from '@/static/assets/upload.svg';
import { addDialogSer, getDialogHistory, getDialogList } from '@/pages/SecLLM/service';
import { SceneList } from '@/pages/SecLLM/constant';

const { Dragger } = Upload;

function UploadFile(props: UploadData) {
  const { inputValue, sceneName, setDialogs, setCurrentDialog, currentDialog, setMessages } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const addDialog = (currentId: string) => {
    const addData = {
      dialog_uuid: currentId,
      dialog_name: '上传文件',
      scene_id: sceneName,
      scene_name: SceneList.filter((item) => item.scene_id === sceneName)[0].scene_name,
    };
    // 调用addDialogSer;，传入addData
    addDialogSer(addData).then(async ({ data }) => {
      if (data) {
        message.success('新建会话成功');
        setTimeout(() => {
          getSideBar();
        }, 1000);
      }
    });
  };
  const handleUpload = () => {
    const formData = new FormData();
    fileList.forEach((file) => {
      formData.append('files', file as RcFile);
    });
    setUploading(true);
    const uploadDialogId = currentDialog === '' ? generateUUID() : currentDialog;
    if (currentDialog === '') {
      addDialog(uploadDialogId);
      setCurrentDialog(uploadDialogId);
    }
    const url = `/api/v1/secllm/knowledge/add?dialog_uuid=${uploadDialogId}`;
     fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        // Authorization: 'Bearer ' + localStorage.getItem('x-auth-token'),
        Authorization: localStorage.getItem('x-auth-token'),
      },
    })
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 200) {
          // message.success('上传成功');
          if (res.data && res.data.length > 0) {
            setTimeout(() => {
              getSideBar();
              setCurrentDialog(res.data[0].dialog_uuid);
            }, 1000);
          }
          setFileList([]);
          // message.success('上传成功');

          // 创建会话
          const data = {
            dialog_uuid: uploadDialogId,
          };
          getMessages(data);
          setIsModalOpen(false);
        } else {
          message.error(res.message);
        }
      })
      .catch(() => {
        // message.error('上传失败');
      })
      .finally(() => {
        setUploading(false);
      });
  };

  const getMessages = (historyData: any) => {
    // 获取历史消息
    getDialogHistory(historyData).then(({ data }) => {
      setMessages(data);
    });
  };
  const getSideBar = () => {
    // 获取会话信息 getDialogList
    getDialogList().then(({ data }) => {
      if (data && data.length > 0) {
        setDialogs(data);
      }
    });
  };

  const UploadProps = {
    onRemove: (file: any) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: any) => {
      // 限制文件类型
      const isAccept = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.csv', '.txt', '.md','.rar','.zip','.json'].includes(
        file.name.substring(file.name.lastIndexOf('.')),
      );
      if (!isAccept) {
        message.error(`${file.name} 文件类型不正确`);
        return;
      }
      // 限制文件数量
      const isLimit = fileList.length < 3;
      if (!isLimit) {
        message.error('最多上传3个文件');
        return;
      }
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  };

  return (
    <div>
      <div className="upload-icon" onClick={showModal}>
        <img src={upload} />
        <span>文档</span>
      </div>
      <Modal
        title="导入文件"
        visible={isModalOpen}
        cancelText="取消"
        okText="上传"
        onOk={handleUpload}
        onCancel={handleCancel}
        confirmLoading={uploading}
      >
        <Dragger {...UploadProps} maxCount={3}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击选择或将文件拖拽到这里上传</p>
          <p className="ant-upload-hint">支持扩展名：.pdf,.doc,.docx,.xls,.xlsx,.csv,.txt,.md,.rar,.zip,.json</p>
        </Dragger>
      </Modal>
    </div>
  );
}

export default UploadFile;
