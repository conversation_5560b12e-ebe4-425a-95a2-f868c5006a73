/* Chatbot.css */
.chatbot-containers {
  position: relative;
  width: 100%;
  min-width: 1000px;
  height: 100%;
  margin: 0 auto;
  padding: 1vw;
  background: #fff;
  // overflow: auto;
  .chat-title {
    font-size: 20px;
  }

  /* 对话框样式 */
  .chat-box {
    position: absolute;
    left: 50%;
    display: flex;
    width: 100%;
    height: 82%;
    padding: 20px 20px;
    overflow-y: auto;
    background: #fff;
    transform: translateX(-50%);
    // box-shadow: 0 30px 20px -24px rgba(0, 0, 0, 0.12);
    .chat-message {
      width: 100%;
      // 水平居中
      margin: 0 auto;
      overflow: auto;
      .ant-list-item {
        padding: 0 !important;
      }
      .dialog-item {
        display: block;
        width: 100%;
        font-size: 16px;
        .message-item {
          display: flex;
          align-items: flex-start; /* 调整消息项垂直对齐方式 */
          width: 100%;
          margin-bottom: 10px;
          a {
            float: right;
            margin-right: 8px;
            color: #aaa;
            font-size: 16px;
          }
          .message-content {
            // max-width: 860px;
            max-width: calc(100% - 112px);
            padding: 15px;
            border-radius: 8px;
          }
          .message-item-right {
            margin-left: auto;
            color: #fff;
            // background-color: #55a722;
            background-color: #6a9352;
          }

          .message-item-left {
            margin-right: auto;
            // background-color: #f7f8fa;
            background-color: #f7f8fa;
          }
        }
      }
    }
  }

  /* 输入框样式 */
  .input-container {
    position: absolute;
    bottom: 1px;
    left: 50%;
    z-index: 8;
    width: 100%;
    margin: 0 auto;
    padding: 20px calc(100vw * (242 / 1920));
    // display: flex;
    background: #fff;
    box-shadow: 0 -12px 22px 0 rgb(255, 255, 255);
    transform: translateX(-50%);
    .upload-container {
      position: relative;
      margin-bottom: 8px;
      cursor: pointer;
      .operate-icon {
        display: flex;
        .upload-icon {
          display: flex;
          align-items: center;
          height: 28px;
          margin-right: 8px;
          padding: 0 8px;
          color: #2e2e2e;
          font-weight: 500;
          background: #f7f8fa;
          border-radius: 4px;
          .tips {
            color: #5c5c5c;
            font-size: 12px;
            animation: blink 1s infinite;
          }
          img {
            margin-right: 6px;
          }
        }
      }
      .stop-dialog {
        position: absolute;
        bottom: 0;
        left: 50%;
        z-index: 99999999;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px !important;
        padding: 0 16px 0 13px;
        color: #2e2e2e;
        font-size: 16px;
        line-height: 40px;
        background: #fff;
        border: 1px solid #d0d2d5;
        border-radius: 8px;
        transform: translateX(-50%);
        img {
          margin-right: 8px;
        }
      }
    }
    .input-group {
      position: relative;
      display: flex;
      width: 100%;
      cursor: pointer;
      .input-icon {
        position: absolute;
        left: -36px;
        color: #aaa;
        font-size: 24px;
        line-height: 54px;
        img {
          color: #aaa;
        }
      }
    }
  }
  .ant-list-item {
    border: none !important;
  }
}
.loading-disable {
  opacity: 0.9;
  cursor: not-allowed;
  opacity: 0.25;
  pointer-events: none;
}
.hot-tag {
  display: inline-block;
  max-width: 120px;
  height: 24px;
  margin-right: 8px;
  padding: 0 8px;
  overflow: hidden;
  font-size: 12px;
  line-height: 24px;
  white-space: nowrap;
  text-overflow: ellipsis;
  border: 1px solid #d9d9d9;
  border-radius: 12px;
  cursor: pointer;
}
.hot-tag:hover {
  color: #55a722;
  border-color: #55a722;
}
@keyframes blink {
  0%,
  100% {
    opacity: 0.3; /* 闪动时的透明度 */
  }
  50% {
    opacity: 1; /* 停止闪动时的透明度 */
  }
}
.disabled-btn {
  cursor: not-allowed;
  opacity: 0.5; /* 设置透明度，使元素看起来被禁用 */
  pointer-events: none; /* 禁用元素的交互功能 */
}

/* 热门推荐样式 */
.hot-container {
  position: absolute;
  left: 50%;
  z-index: 99;
  width: 100%;
  height: 70%;
  margin: 0 auto;
  padding: 25px calc(100vw * (242 / 1920));
  background: #fff;
  transform: translateX(-50%);
  .title-name {
    padding: 10px 0 64px 0;
    font-weight: 700;
    font-size: 40px;
    text-align: center;
  }
  .hot-title {
    // 文字上下居中
    display: flex;
    align-items: center;
    height: 24px;
    margin-bottom: 8px;
    color: #2e2e2e;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: left;
    span {
      padding-right: 8px;
      font-size: 24px;
    }
  }
  .hot-content {
    // display: flex;
    .hot-item {
      margin-top: 10px;
      padding: 10px 15px;
      color: #535353;
      font-size: 16px;
      background-color: #f7f8fa;
      border-radius: 8px;
      // 自动换行
      word-break: break-all;
      // 最高高度为90px, 超过高度的部分隐藏，显示省略号
      max-height: 90px;
      overflow: auto;
    }
    // 鼠标滑过hot-item的效果
    .hot-item:hover {
      // color: #55a722!important;
      background-color: #f0f1f3 !important;
      cursor: pointer;
    }
  }
}
.feed-back {
  height: 28px;
  margin-top: 8px;
}
.ant-form-item {
  margin-bottom: 16px !important;
}
.ant-modal-body {
  padding: 24px 24px 10px 24px !important;
}
.ant-list-empty-text {
  // 上下居中
  margin-top: 20%;
}
.feed-back a:hover {
  color: #55a722 !important;
}
.feed-icon {
  display: flex;
  align-items: center;
  .feedback-modal-title {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
  }
  .text-color {
    color: #2e2e2e;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: left;
  }
}

.like-icon {
  color: #68b92e;
  background: rgba(104, 185, 46, 0.2);
}
.dislike-icon {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.2);
}
// .input-container .input-group .ant-select-selector {
//   height: 54px !important;
//   font-size: 16px !important;
//   border-right: 0 !important;
// }
.select-scene {
  height: 30px;
  margin-right: 8px;
  background-color: #f7f8fa;
  border-radius: 4px;
}
// .input-container .input-group .scene-input .ant-select .ant-select-selector,.ant-select-selection-item {
//   font-size: 16px !important;
//   line-height: 52px !important;
//   border-right: 0 !important;
// }
// .input-container .input-group .ant-input:placeholder-shown,.ant-select-selection-search {
//   line-height: 42px !important;
// }
// .input-container .input-group .ant-input {
//   border-right: 0 !important;
//   border-left: 0 !important;
// }
// .input-container .input-group .ant-btn {
//   height: 54px !important;
//   font-size: 16px !important;
//   border-left: 0 !important;
//   border-radius: 0 10px 10px 0 !important;
// }
.ant-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
}
.avatar-user {
  margin-left: 8px;
  padding: 6px 9px;
  background: #fff;
  border: 1px solid #dee0e3;
}
.avatar-container {
  flex: none; /* 让头像容器不拉伸 */
  align-self: flex-start; /* 让头像自身靠上对齐 */
}
.avatar-chatbot {
  margin-right: 8px;
  padding: 5px 6px;
  background: linear-gradient(180deg, #4e5e6c, #26303a);
  // border: 1px solid #26303a;
}
.timestamp {
  color: #999;
  font-size: 12px;
  text-align: right;
}
.avatar-right {
  margin-left: auto;
}

.avatar-left {
  margin-right: auto;
}

// 写一段CSS代码，进行滚动条样式优化
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}
::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}

// 进度条展示
.loader {
  position: relative;
  z-index: -1;
  width: calc(100% - 14px);
  height: 5px;
  margin-top: -1px;
  margin-left: 7px;
  background: linear-gradient(90deg, yellow, green, blue);
  background-size: 600%;
  border-radius: 0 0 10px 10px;
  animation: loader 22s linear infinite;
}
.loader::before {
  position: absolute;
  background: linear-gradient(90deg, yellow, green, blue);
  background-size: 600%;
  border-radius: 0 0 10px 10px;
  animation: loader 22s linear infinite;
  content: '';
  inset: 0;
  // filter: blur(20px);
}
@keyframes loader {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 600% 0;
  }
}
