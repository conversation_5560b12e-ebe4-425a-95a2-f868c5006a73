import { CopyOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
const CopyText = (props: { copyData: any }) => {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopyClick = () => {
    if (props.copyData) {
      navigator.clipboard
        .writeText(props.copyData)
        .then(() => {
          setIsCopied(true);
        })
        .catch((error) => {
          console.error('复制失败', error);
        });
    }
  };
  useEffect(() => {
    if (isCopied===true) {
      //   时隔5秒isCopied自动变为false;
      setTimeout(() => {
        setIsCopied(false);
      }, 5000);
    }
  }, [isCopied]);
  return (
    <span onClick={handleCopyClick}>
      <CopyOutlined />
      {isCopied ? '√' : ''}
    </span>
  );
};

export default CopyText;
