import React from 'react';
import { Sankey } from '@ant-design/plots';

const SankeyChart = (props: any) => {
  const { data } = props;
  const config = {
    data: data.sankey,
    sourceField: 'source',
    targetField: 'target',
    weightField: 'value',
    nodeWidthRatio: 0.025,
    nodePaddingRatio: 0.02,
    edgeStyle: {
      opacity: 0.6,
      lineWidth: 1,
    },
  };
  if (data.sankey) {
  return <Sankey {...config} />;
  }else{
    return ''
  }
};

export default React.memo(SankeyChart);
