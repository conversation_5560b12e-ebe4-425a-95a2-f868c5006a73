import { <PERSON><PERSON>, <PERSON>, <PERSON>, Collapse, <PERSON>, Tabs, Tree } from "antd";
import ChatBot from "./components/ChatBot";
import { useEffect, useState } from "react";
import { ToolOutlined, ApiOutlined, AppstoreOutlined, PlusOutlined, PartitionOutlined, SafetyOutlined, SecurityScanOutlined, ShareAltOutlined, CopyOutlined, ProjectOutlined } from "@ant-design/icons";
import { DataNode } from "antd/lib/tree";
const { TabPane } = Tabs;
const { Panel } = Collapse;
export default function SecAgent() {
    const [currentDialog, setCurrentDialog] = useState('');
    const [dialogs, setDialogs] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [typingText, setTypingText] = useState(false); // 保存正在打字的内容
    const [messages, setMessages] = useState([]);
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
    const [sceneData, setSceneData] = useState({
        scene_name: '',
        scene_id: '',
        model_api: '',
        scene_desc: '',
        knowledge_base_id: '',
    });

    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['0-0-0', '0-0-1', '0-0-2', '0-1-0', '0-1-1', '0-2-0']);

    const treeData: DataNode[] = [
        {
            title: '威胁情报',
            key: '0-10',
            icon: <AppstoreOutlined />,
            children: [
                {
                    title: 'IP情报', key: 'ip_ti', icon: <ToolOutlined />, children: [
                        { title: '查询下*************关联的威胁情报', key: 'ip_ti_0', icon: <CopyOutlined /> },
                        { title: '查询下*************关联的威胁情报', key: 'ip_ti_1', icon: <CopyOutlined /> }
                    ]
                },
                {
                    title: '域名情报', key: 'domain_ti', icon: <ToolOutlined />, children: [
                        { title: '查询下nsfocus.com存在哪些子域名', key: 'domain_ti_0', icon: <CopyOutlined /> },
                        { title: '查询下nsfocus.com的whois信息', key: 'domain_ti_1', icon: <CopyOutlined /> }
                    ]
                },
                {
                    title: '攻击组织', key: 'apt_ti', icon: <ToolOutlined />, children: [
                        { title: '查询下APT32攻击组织相关信息', key: 'apt_ti_0', icon: <CopyOutlined /> },
                        { title: '查询下海莲花惯用恶意样本', key: 'apt_ti_1', icon: <CopyOutlined /> }
                    ]
                }
            ]
        },
        {
            title: '信息收集',
            key: '0-0',
            icon: <AppstoreOutlined />,
            children: [{
                title: '资产收集', key: '0-0-1', children: [
                    { title: 'enscan', key: 'enscan', isLeaf: true, icon: <ToolOutlined /> },
                    { title: 'ovo', key: 'ovo', isLeaf: true, icon: <ToolOutlined />, }
                ]
            },
            {
                title: '端口扫描', key: '0-0-2', children: [
                    {
                        title: 'nmap', key: 'nmap', isLeaf: true, icon: <ToolOutlined />, children: [
                            { title: '请用nmap帮我查看10.24.45.214开放的所有端口', key: 'nmap_0', icon: <CopyOutlined /> },
                            { title: '请用nmap帮我查看10.24.45.214是否开启了哪些web服务', key: 'nmap_1', icon: <CopyOutlined /> }
                        ]
                    },
                    { title: 'masscan', key: 'masscan', isLeaf: true, icon: <ToolOutlined />, },
                    {
                        title: 'TXPortMap', key: 'TXPortMap', icon: <ToolOutlined />, children: [
                            { title: '使用 TXPortMap 扫描10.24.45.214的80、8080端口是否开放', key: 'TXPortMap_0', icon: <CopyOutlined /> },
                            { title: '使用 TXPortMap 扫描目标文件target.txt的top1000端口，设置线程数为10', key: 'TXPortMap_1', icon: <CopyOutlined /> }
                        ]
                    }
                ]
            },
            {
                title: '子域名扫描', key: '0-0-3', children: [
                    {
                        title: 'oneforall', key: 'oneforall', icon: <ToolOutlined />, children: [
                            { title: '扫描目标子域名，只导出存活的子域', key: 'oneforall_0', icon: <CopyOutlined /> },
                            { title: '扫描目标文件target.txt中url的子域名，并检查子域接管，将结果保存为json格式', key: 'oneforall_1', icon: <CopyOutlined /> }
                        ]
                    }
                ]
            },
            {
                title: '目录扫描', key: '0-0-4', children: [
                    {
                        title: 'dirmap', key: 'dirmap', icon: <ToolOutlined />, children: [
                            { title: '使用 dirmap 扫描目标 http://10.24.45.214:8001/ 目录', key: 'dirmap_0', icon: <CopyOutlined /> },
                        ]
                    },
                    {
                        title: 'dirsearch', key: 'dirsearch', icon: <ToolOutlined />, children: [
                            { title: '使用 dirsearch 扫描一下 http://10.24.45.214:8001/ 的目录', key: 'dirsearch_0', icon: <CopyOutlined /> },
                        ]
                    }
                ]
            },
            ],
        },
        {
            title: '外网打点',
            key: '0-1',
            icon: <PartitionOutlined />,
            children: [
                {
                    title: '漏洞扫描', key: '0-1-0', children: [
                        { title: 'sqlmap', key: 'sqlmap', isLeaf: true, icon: <ToolOutlined />, },
                        {
                            title: 'EZ', key: 'EZ', icon: <ToolOutlined />, children: [
                                { title: '利用 EZ 工具批量检测Log4j远程代码执行漏洞', key: 'EZ_0', icon: <CopyOutlined /> },
                                { title: '请用 ez 工具扫描http://10.24.45.214:8983/', key: 'EZ_1', icon: <CopyOutlined /> }
                            ]
                        }
                    ]
                },
                {
                    title: '漏洞利用', key: '0-1-1', children: [
                        { title: 'firebox', key: 'firebox', isLeaf: true, icon: <ToolOutlined />, },
                    ]
                },
            ],
        },
        {
            title: '内网渗透',
            key: '0-2',
            icon: <SecurityScanOutlined />,
            children: [
                {
                    title: '内网扫描', key: '0-2-0', children: [
                        {
                            title: 'fscan', key: 'fscan', icon: <ToolOutlined />, children: [
                                { title: '使用fscan 对 10.24.45.214 进行扫描，跳过存活检测 、跳过web poc扫描、跳过暴力破解扫描', key: 'fscan_0', icon: <CopyOutlined /> },
                                { title: '使用fscan，用密码1qaz!QAZ对10.24.45.214的b段进行ssh爆破，扫描端口为22、62222', key: 'fscan_1', icon: <CopyOutlined /> }
                            ]
                        },
                    ]
                },
            ],
        },
    ];

    const onSelect = (selectedKeysValue: React.Key[], info: any) => {
        console.log('onSelect', info, selectedKeysValue);

        setSelectedKeys(selectedKeysValue);
        setInputValue(info.node.title)
    }
    // 新建会话
    const addDialog = () => {
        setCurrentDialog('');
        // setSceneData(SceneList[0]);  
        setMessages([]);
    };

    return (
        <Row style={{ background: "#F0F2F5" }} >
            <Col span={6} >
                <Card size='small' bordered={false} style={{ minHeight: '90vh' }}

                >
                    {/* <header className="padd-borders">
                        <h3>工具链 - 点击复制使用例子</h3>
                    </header> */}

                    <Tabs defaultActiveKey="1" tabPosition="top"
                        tabBarExtraContent={<Button
                            type="primary"
                            onClick={addDialog}
                        >
                            <PlusOutlined />
                            新建会话
                        </Button>} >
                        {/* 对话框 */}
                        <TabPane tab={[<ProjectOutlined />, "攻防工具链"]} key="1" style={{ maxWidth: '40vw', overflow: 'auto', minHeight: '80vh', background: '#F0F2F5' }}>
                            <Tree
                                style={{ background: '#F0F2F5', paddingTop: '10px' }}
                                defaultExpandedKeys={expandedKeys}
                                onSelect={onSelect}
                                autoExpandParent={true}
                                treeData={treeData}
                                showIcon
                            />
                        </TabPane>
                        {/* <TabPane tab="我创建的" key="2" style={{ height: '80vh' }}>

                        </TabPane> */}

                    </Tabs>
                </Card>
            </Col>
            <Col span={18} >
                <ChatBot
                    key={'secagent_chatbot'}
                    currentDialog={currentDialog}
                    setCurrentDialog={setCurrentDialog}
                    setDialogs={setDialogs}
                    inputValue={inputValue}
                    setInputValue={setInputValue}
                    typingText={typingText}
                    setTypingText={setTypingText}
                    messages={messages}
                    dialogs={dialogs}
                    sceneData={sceneData}
                    setMessages={setMessages}
                    setSceneData={setSceneData}
                />

            </Col>

        </Row>
    )
}